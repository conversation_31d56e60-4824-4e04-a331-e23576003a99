
powershell "& 'C:\Installation\ngrok.exe' config add-authtoken 566Vj5CAqC5YUnF3PLEhJ_5k8ymj1vi4G8dDRWkfSXf"{
  "name": "ElthosRPG_Blog_Twitter",
  "nodes": [
    {
      "parameters": {
        "url": "={{ $json.selectedPostURL }}",
        "responseFormat": "string",
        "options": {}
      },
      "name": "Fetch Post",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [
        0,
        60
      ],
      "id": "c9fe53ca-3a67-4ee9-9db5-d776584a8dcb"
    },
    {
      "parameters": {},
      "type": "n8n-nodes-base.manualTrigger",
      "typeVersion": 1,
      "position": [
        0,
        -380
      ],
      "id": "e7a7f4bd-3ae8-49c7-aafd-b15738091113",
      "name": "When clicking 'Execute workflow'",
      "disabled": true
    },
    {
      "parameters": {
        "assignments": {
          "assignments": [
            {
              "id": "356721e2-5dce-41fa-9118-1891bca27394",
              "name": "mainBlogURL",
              "value": "https://elthosrpg.blogspot.com/",
              "type": "string"
            }
          ]
        },
        "options": {}
      },
      "type": "n8n-nodes-base.set",
      "typeVersion": 3.4,
      "position": [
        180,
        -200
      ],
      "id": "8d837417-3896-4526-a97a-b0ef365678e0",
      "name": "Set Main Blog URL"
    },
    {
      "parameters": {
        "url": "={{ $json.mainBlogURL }}",
        "responseFormat": "string",
        "options": {}
      },
      "name": "Fetch Blog Page",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [
        360,
        -200
      ],
      "id": "40c12ed9-47e2-4d68-9e08-654690c06192"
    },
    {
      "parameters": {
        "operation": "extract-content",
        "html": "={{ $json.data }}"
      },
      "type": "n8n-nodes-scrapeninja.scrapeNinja",
      "typeVersion": 1,
      "position": [
        540,
        -200
      ],
      "id": "cdde7e9f-d9c1-48c1-a0bf-9f4d937f0ea8",
      "name": "Scrape Blog Page"
    },
    {
      "parameters": {
        "jsCode": "// Get raw HTML from Fetch Blog Page\nconst rawHtml = $node['Fetch Blog Page'].json.data;\nconst mainBlogURL = $input.first().json.mainBlogURL;\n\nconsole.log('Extracting URLs from raw HTML...');\nconsole.log('Raw HTML length:', rawHtml.length);\n\n// Simple regex to find href='https://elthosrpg.blogspot.com/YYYY/MM/post-name.html'\nconst matches = rawHtml.match(/href='https:\\/\\/elthosrpg\\.blogspot\\.com\\/\\d{4}\\/\\d{2}\\/[^']+\\.html'/g) || [];\n\nconsole.log('Found', matches.length, 'href matches');\n\nlet postUrls = [];\nmatches.forEach(match => {\n  // Extract URL from href='URL'\n  const url = match.replace(/href='/, '').replace(/'$/, '');\n  if (url.includes('/20') && !postUrls.includes(url)) {\n    postUrls.push(url);\n    console.log('Added URL:', url);\n  }\n});\n\nconsole.log('Total unique URLs:', postUrls.length);\n\n// If no URLs found, show debug info\nif (postUrls.length === 0) {\n  console.log('No URLs found! HTML preview:', rawHtml.substring(0, 500));\n  return [{ \n    postUrls: [],\n    totalPosts: 0,\n    mainBlogURL: mainBlogURL,\n    error: 'No URLs found',\n    htmlPreview: rawHtml.substring(0, 500)\n  }];\n}\n\nreturn [{ \n  postUrls: postUrls,\n  totalPosts: postUrls.length,\n  mainBlogURL: mainBlogURL\n}];"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1060,
        -200
      ],
      "id": "b42d8fd6-5673-4fdc-8fdd-2b675b0bc3ba",
      "name": "Extract Post URLs"
    },
    {
      "parameters": {
        "jsCode": "// Randomly select a blog post URL\nconst postUrls = $input.first().json.postUrls;\nconst totalPosts = $input.first().json.totalPosts;\nconst mainBlogURL = $input.first().json.mainBlogURL;\n\nconsole.log(`Selecting random post from ${totalPosts} available posts...`);\n\n// Generate random index\nconst randomIndex = Math.floor(Math.random() * postUrls.length);\nconst selectedPostURL = postUrls[randomIndex];\n\nconsole.log(`Selected post: ${selectedPostURL}`);\n\n// Return the selected URL along with metadata\nreturn [{ json: { \n  selectedPostURL: selectedPostURL,\n  randomIndex: randomIndex,\n  totalPosts: totalPosts,\n  mainBlogURL: mainBlogURL\n}}];"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1260,
        -200
      ],
      "id": "63f9d0e1-2669-4d1a-aa66-918704eb45b3",
      "name": "Random Selection"
    },
    {
      "parameters": {
        "promptType": "define",
        "text": "={{ $json.firstParagraph }}",
        "messages": {
          "messageValues": [
            {
              "message": "You are a twitter expert who can take the best content from the blog entry and make the perfect tweet from it that is no more than 60 characters long. Use Text format. Include hash tags #IndieRPG #Elthos #TTRPG. Be concise and engaging."
            }
          ]
        },
        "batching": {}
      },
      "type": "@n8n/n8n-nodes-langchain.chainLlm",
      "typeVersion": 1.7,
      "position": [
        740,
        60
      ],
      "id": "41277a04-fc9f-4b6b-9ff5-6429a15a5a2b",
      "name": "Basic LLM Chain",
      "alwaysOutputData": false
    },
    {
      "parameters": {
        "model": {
          "__rl": true,
          "value": "deepseek-r1-distill-llama-8b",
          "mode": "list",
          "cachedResultName": "deepseek-r1-distill-llama-8b"
        },
        "options": {}
      },
      "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi",
      "typeVersion": 1.2,
      "position": [
        740,
        240
      ],
      "id": "9be2e356-34b3-4cb3-a67d-17c7f515d063",
      "name": "OpenAI Chat Model",
      "credentials": {
        "openAiApi": {
          "id": "cqdpm9ID0q2zjSkV",
          "name": "LM Studio"
        }
      }
    },
    {
      "parameters": {
        "operation": "extract-content",
        "html": "={{ $('Fetch Post').item.json.data }}",
        "outputMarkdown": true
      },
      "type": "n8n-nodes-scrapeninja.scrapeNinja",
      "typeVersion": 1,
      "position": [
        360,
        60
      ],
      "id": "7cdda613-7380-4723-87d9-2d43e26ae3ef",
      "name": "ScrapeNinja"
    },
    {
      "parameters": {
        "text": "={{ $json.text }}",
        "additionalFields": {}
      },
      "type": "n8n-nodes-base.twitter",
      "typeVersion": 2,
      "position": [
        1260,
        60
      ],
      "id": "25dc21a9-fa3a-43e2-a2a7-8546849906ca",
      "name": "Create Tweet",
      "credentials": {
        "twitterOAuth2Api": {
          "id": "QMuVcnYLarrzm0jp",
          "name": "X account"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// Replace \"content\" with the actual field name from ScrapeNinja output\nconst content = $input.first().json.content;\n\n// Split into paragraphs by double line breaks\nconst paragraphs = content.split(/\\n\\s*\\n/);\n\n// Get the first paragraph\nlet firstParagraph = paragraphs[2] || \"\";\n\n// Limit to 3000 words\nconst words = firstParagraph.split(/\\s+/).slice(0, 3000);\nconst limitedParagraph = words.join(\" \");\n\n// PRESERVE the blogURL field from input\nconst blogURL =   $('Random Selection').first().json.selectedPostURL \n\nconsole.log('blogURL: ' + blogURL);\n\nreturn [{ json: { \n    firstParagraph: limitedParagraph,\n    blogURL: blogURL \n}}];\n"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        540,
        60
      ],
      "id": "d358a0ff-c247-498a-bd7e-d715061484a2",
      "name": "Code - Get Paragraph 1"
    },
    {
      "parameters": {
        "jsCode": "// Get the text from the LLM\nvar text = $input.first().json.text;\n\n// Try different ways to get the blog URL\nlet blogUrl = null;\n\n// Method 1: Try to access the Edit Field node directly\ntry {\n  const blogUrlNode = $('Edit Field - blogURL');\n  if (blogUrlNode && blogUrlNode.first()) {\n    blogUrl = blogUrlNode.first().json.blogURL;\n    console.log('Got blog URL from direct access:', blogUrl);\n  }\n} catch (error) {\n  console.log('Direct access failed');\n}\n\n// Method 2: If that doesn't work, check if it's in the input data\nif (!blogUrl && $input.first().json.blogURL) {\n  blogUrl = $input.first().json.blogURL;\n  console.log('Got blog URL from input:', blogUrl);\n}\n\n// Method 3: Hardcode as fallback (replace with your actual URL)\nif (!blogUrl) {\n  blogUrl = 'https://ElthosRPG.Blogspot.com'; // Replace with your actual blog URL\n  console.log('Using hardcoded blog URL:', blogUrl);\n}\n\nconsole.log('LLM text:', text);\nconsole.log('Final blog URL:', blogUrl);\n\n// Remove <think>...</think> and everything in between\nconst cleaned = text.replace(/<think>[\\s\\S]*?<\\/think>/, '').trim();\n\n// Clean whitespace\ntext = cleaned.replace(/^\\s+|\\s+$/g, '');\n\nif (text.length >= 2 && text.charAt(0) === '\"' && text.charAt(text.length - 1) === '\"') {\n    text = text.substring(1, text.length - 1);\n}\n\n// Twitter character limit\nconst TARGET_LIMIT = 280;\n\n// Try the full text + URL first\nconst testTweet = text + ' ' + blogUrl;\nconsole.log('Test tweet length:', testTweet.length);\n\n// If it fits within limit, use it as-is\nif (testTweet.length <= TARGET_LIMIT) {\n    console.log('Tweet fits within limit, using full text');\n    return [{ json: { text: testTweet } }];\n}\n\n// If too long, try to preserve hashtags by truncating main content\nconst hashtagRegex = /#\\w+/g;\nconst hashtags = text.match(hashtagRegex) || [];\nconst hashtagsText = hashtags.join(' ');\n\n// Remove hashtags and em-dashes from main text to see content length\nconst contentWithoutHashtags = text.replace(hashtagRegex, '').replace(/—/g, '').replace(/\\s+/g, ' ').trim();\n\n// More precise calculation: content + \" \" + hashtags + \" \" + URL = TARGET_LIMIT\n// So: content = TARGET_LIMIT - hashtags.length - URL.length - 2 spaces - 3 for \"...\"\nconst spacesNeeded = 2; // one before hashtags, one before URL\nconst ellipsisLength = 3;\nconst availableForContent = TARGET_LIMIT - hashtagsText.length - blogUrl.length - spacesNeeded - ellipsisLength;\n\nconsole.log('Available space for content:', availableForContent);\nconsole.log('Content without hashtags:', contentWithoutHashtags);\nconsole.log('Hashtags found:', hashtags);\nconsole.log('Hashtags text length:', hashtagsText.length);\n\n// If we have enough space, use truncated content + hashtags\nif (availableForContent > 15 && hashtags.length > 0) {\n    const truncatedContent = contentWithoutHashtags.substring(0, availableForContent) + '...';\n    const finalTweet = truncatedContent + ' ' + hashtagsText + ' ' + blogUrl;\n    console.log('Using truncated content with hashtags');\n    console.log('Final tweet length:', finalTweet.length);\n    return [{ json: { text: finalTweet } }];\n}\n\n// Fallback: just truncate everything\nconst maxTextLength = TARGET_LIMIT - blogUrl.length - 1 - 3;\nconst truncatedText = text.substring(0, maxTextLength) + '...';\nconst fallbackTweet = truncatedText + ' ' + blogUrl;\n\nreturn [{ json: { text: fallbackTweet } }];"
      },
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1060,
        60
      ],
      "id": "6603c565-bb1c-428b-8dd2-c916d2665260",
      "name": "Code - Clean for Tweet"
    },
    {
      "parameters": {
        "assignments": {
          "assignments": [
            {
              "id": "356721e2-5dce-41fa-9118-1891bca27394",
              "name": "blogURL",
              "value": "={{ $('Random Selection').item.json.selectedPostURL }}",
              "type": "string"
            }
          ]
        },
        "options": {}
      },
      "type": "n8n-nodes-base.set",
      "typeVersion": 3.4,
      "position": [
        180,
        60
      ],
      "id": "be724dfc-4c2c-40a5-aaef-86ed53e7b11b",
      "name": "Edit Field - blogURL"
    },
    {
      "parameters": {
        "rule": {
          "interval": [
            {
              "triggerAtHour": 19
            }
          ]
        }
      },
      "type": "n8n-nodes-base.scheduleTrigger",
      "typeVersion": 1.2,
      "position": [
        0,
        -200
      ],
      "id": "5e993044-5332-489d-968f-89bc387cb31d",
      "name": "Schedule Trigger"
    }
  ],
  "pinData": {},
  "connections": {
    "When clicking 'Execute workflow'": {
      "main": [
        []
      ]
    },
    "Set Main Blog URL": {
      "main": [
        [
          {
            "node": "Fetch Blog Page",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Fetch Blog Page": {
      "main": [
        [
          {
            "node": "Scrape Blog Page",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Scrape Blog Page": {
      "main": [
        [
          {
            "node": "Extract Post URLs",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Extract Post URLs": {
      "main": [
        [
          {
            "node": "Random Selection",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Random Selection": {
      "main": [
        [
          {
            "node": "Fetch Post",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Fetch Post": {
      "main": [
        [
          {
            "node": "Edit Field - blogURL",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Basic LLM Chain": {
      "main": [
        [
          {
            "node": "Code - Clean for Tweet",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "OpenAI Chat Model": {
      "ai_languageModel": [
        [
          {
            "node": "Basic LLM Chain",
            "type": "ai_languageModel",
            "index": 0
          }
        ]
      ]
    },
    "ScrapeNinja": {
      "main": [
        [
          {
            "node": "Code - Get Paragraph 1",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Code - Get Paragraph 1": {
      "main": [
        [
          {
            "node": "Basic LLM Chain",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Code - Clean for Tweet": {
      "main": [
        [
          {
            "node": "Create Tweet",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Edit Field - blogURL": {
      "main": [
        [
          {
            "node": "ScrapeNinja",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Schedule Trigger": {
      "main": [
        [
          {
            "node": "Set Main Blog URL",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": true,
  "settings": {
    "executionOrder": "v1",
    "callerPolicy": "workflowsFromSameOwner"
  },
  "versionId": "9ba60d54-4379-41fd-8dac-e1674039e2b7",
  "meta": {
    "templateCredsSetupCompleted": true,
    "instanceId": "a9e00de748ec35ee88db078f832d6e48181d32e4fa741d36554310dd025f8599"
  },
  "id": "M4PfByDgjUvSFPqZ",
  "tags": []
}