{"name": "ElthosRPG_Blog_Twitter", "nodes": [{"parameters": {"rule": {"interval": [{"triggerAtHour": 19}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, -200], "id": "5e993044-5332-489d-968f-89bc387cb31d", "name": "Schedule Trigger"}, {"parameters": {"assignments": {"assignments": [{"id": "356721e2-5dce-41fa-9118-1891bca27394", "name": "blogURL", "value": "={{ $('Random Selection').item.json.selectedPostURL }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [180, 60], "id": "be724dfc-4c2c-40a5-aaef-86ed53e7b11b", "name": "Edit Field - blogURL"}, {"parameters": {"jsCode": "// Get the text from the LLM\nvar text = $input.first().json.text;\n\n// Try different ways to get the blog URL\nlet blogUrl = null;\n\n// Method 1: Try to access the Edit Field node directly\ntry {\n  const blogUrlNode = $('Edit Field - blogURL');\n  if (blogUrlNode && blogUrlNode.first()) {\n    blogUrl = blogUrlNode.first().json.blogURL;\n    console.log('Got blog URL from direct access:', blogUrl);\n  }\n} catch (error) {\n  console.log('Direct access failed');\n}\n\n// Method 2: If that doesn't work, check if it's in the input data\nif (!blogUrl && $input.first().json.blogURL) {\n  blogUrl = $input.first().json.blogURL;\n  console.log('Got blog URL from input:', blogUrl);\n}\n\n// Method 3: Hardcode as fallback (replace with your actual URL)\nif (!blogUrl) {\n  blogUrl = 'https://ElthosRPG.Blogspot.com'; // Replace with your actual blog URL\n  console.log('Using hardcoded blog URL:', blogUrl);\n}\n\nconsole.log('LLM text:', text);\nconsole.log('Final blog URL:', blogUrl);\n\n// Remove <think>...</think> and everything in between\nconst cleaned = text.replace(/<think>[\\s\\S]*?<\\/think>/, '').trim();\n\n// Clean whitespace\ntext = cleaned.replace(/^\\s+|\\s+$/g, '');\n\nif (text.length >= 2 && text.charAt(0) === '\"' && text.charAt(text.length - 1) === '\"') {\n    text = text.substring(1, text.length - 1);\n}\n\n// Twitter character limit\nconst TARGET_LIMIT = 280;\n\n// Try the full text + URL first\nconst testTweet = text + ' ' + blogUrl;\nconsole.log('Test tweet length:', testTweet.length);\n\n// If it fits within limit, use it as-is\nif (testTweet.length <= TARGET_LIMIT) {\n    console.log('Tweet fits within limit, using full text');\n    return [{ json: { text: testTweet } }];\n}\n\n// If too long, try to preserve hashtags by truncating main content\nconst hashtagRegex = /#\\w+/g;\nconst hashtags = text.match(hashtagRegex) || [];\nconst hashtagsText = hashtags.join(' ');\n\n// Remove hashtags and em-dashes from main text to see content length\nconst contentWithoutHashtags = text.replace(hashtagRegex, '').replace(/—/g, '').replace(/\\s+/g, ' ').trim();\n\n// More precise calculation: content + \" \" + hashtags + \" \" + URL = TARGET_LIMIT\n// So: content = TARGET_LIMIT - hashtags.length - URL.length - 2 spaces - 3 for \"...\"\nconst spacesNeeded = 2; // one before hashtags, one before URL\nconst ellipsisLength = 3;\nconst availableForContent = TARGET_LIMIT - hashtagsText.length - blogUrl.length - spacesNeeded - ellipsisLength;\n\nconsole.log('Available space for content:', availableForContent);\nconsole.log('Content without hashtags:', contentWithoutHashtags);\nconsole.log('Hashtags found:', hashtags);\nconsole.log('Hashtags text length:', hashtagsText.length);\n\n// If we have enough space, use truncated content + hashtags\nif (availableForContent > 15 && hashtags.length > 0) {\n    const truncatedContent = contentWithoutHashtags.substring(0, availableForContent) + '...';\n    const finalTweet = truncatedContent + ' ' + hashtagsText + ' ' + blogUrl;\n    console.log('Using truncated content with hashtags');\n    console.log('Final tweet length:', finalTweet.length);\n    return [{ json: { text: finalTweet } }];\n}\n\n// Fallback: just truncate everything\nconst maxTextLength = TARGET_LIMIT - blogUrl.length - 1 - 3;\nconst truncatedText = text.substring(0, maxTextLength) + '...';\nconst fallbackTweet = truncatedText + ' ' + blogUrl;\n\nreturn [{ json: { text: fallbackTweet } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1060, 60], "id": "6603c565-bb1c-428b-8dd2-c916d2665260", "name": "Code - Clean for Tweet"}, {"parameters": {"jsCode": "// Replace \"content\" with the actual field name from ScrapeNinja output\nconst content = $input.first().json.content;\n\n// Split into paragraphs by double line breaks\nconst paragraphs = content.split(/\\n\\s*\\n/);\n\n// Get the first paragraph\nlet firstParagraph = paragraphs[2] || \"\";\n\n// Limit to 3000 words\nconst words = firstParagraph.split(/\\s+/).slice(0, 3000);\nconst limitedParagraph = words.join(\" \");\n\n// PRESERVE the blogURL field from input\nconst blogURL =   $('Random Selection').first().json.selectedPostURL \n\nconsole.log('blogURL: ' + blogURL);\n\nreturn [{ json: { \n    firstParagraph: limitedParagraph,\n    blogURL: blogURL \n}}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [540, 60], "id": "d358a0ff-c247-498a-bd7e-d715061484a2", "name": "Code - Get Paragraph 1"}, {"parameters": {"text": "={{ $json.text }}", "additionalFields": {}}, "type": "n8n-nodes-base.twitter", "typeVersion": 2, "position": [1260, 60], "id": "25dc21a9-fa3a-43e2-a2a7-8546849906ca", "name": "Create Tweet", "credentials": {"twitterOAuth2Api": {"id": "QMuVcnYLarrzm0jp", "name": "X account"}}}, {"parameters": {"operation": "extract-content", "html": "={{ $('Fetch Post').item.json.data }}", "outputMarkdown": true}, "type": "n8n-nodes-scrapeninja.scrapeNinja", "typeVersion": 1, "position": [360, 60], "id": "7cdda613-7380-4723-87d9-2d43e26ae3ef", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"parameters": {"model": {"__rl": true, "value": "deepseek-r1-distill-llama-8b", "mode": "list", "cachedResultName": "deepseek-r1-distill-llama-8b"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [740, 240], "id": "9be2e356-34b3-4cb3-a67d-17c7f515d063", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "cqdpm9ID0q2zjSkV", "name": "LM Studio"}}}, {"parameters": {"promptType": "define", "text": "={{ $json.firstParagraph }}", "messages": {"messageValues": [{"message": "You are a pithing twitter expert with a personal touch who can take the best content from the blog entry and make the perfect tweet from it that is no more than 60 characters long. Use Text format. Include hash tags #IndieRPG #Elthos #TTRPG. Be concise and engaging."}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [740, 60], "id": "41277a04-fc9f-4b6b-9ff5-6429a15a5a2b", "name": "Basic LLM Chain", "alwaysOutputData": false}, {"parameters": {"jsCode": "const fs = require('fs');\nconst path = require('path');\n\n// Get input data\nconst postUrls = $input.first().json.postUrls;\nconst totalPosts = $input.first().json.totalPosts;\nconst mainBlogURL = $input.first().json.mainBlogURL;\n\nconsole.log(`Starting selection from ${totalPosts} available posts...`);\n\n// Define exclusion file path\nconst exclusionFile = path.join(__dirname, '..', '..', 'projects', 'elthosdb1', 'ExcludeBlogPosts.txt');\n\n// Read existing exclusion list\nlet excludedUrls = [];\ntry {\n  if (fs.existsSync(exclusionFile)) {\n    const fileContent = fs.readFileSync(exclusionFile, 'utf8');\n    excludedUrls = fileContent.split('\\n').filter(url => url.trim() !== '');\n    console.log(`Loaded ${excludedUrls.length} excluded URLs`);\n  } else {\n    console.log('No exclusion file found, starting fresh');\n  }\n} catch (error) {\n  console.log('Error reading exclusion file:', error.message);\n}\n\n// Filter out excluded URLs\nconst availableUrls = postUrls.filter(url => !excludedUrls.includes(url));\nconsole.log(`Available posts after exclusion: ${availableUrls.length}`);\n\n// If no available URLs (all excluded), reset the exclusion list\nif (availableUrls.length === 0) {\n  console.log('All posts excluded! Resetting exclusion list...');\n  excludedUrls = [];\n  availableUrls.push(...postUrls);\n}\n\n// Randomly select from available URLs\nconst randomIndex = Math.floor(Math.random() * availableUrls.length);\nconst selectedPostURL = availableUrls[randomIndex];\n\nconsole.log(`Selected post: ${selectedPostURL}`);\n\n// Add selected URL to exclusion list\nexcludedUrls.push(selectedPostURL);\n\n// Keep only the most recent 20 entries\nif (excludedUrls.length > 20) {\n  excludedUrls = excludedUrls.slice(-20);\n}\n\n// Write updated exclusion list back to file\ntry {\n  fs.writeFileSync(exclusionFile, excludedUrls.join('\\n'), 'utf8');\n  console.log(`Updated exclusion list with ${excludedUrls.length} entries`);\n} catch (error) {\n  console.log('Error writing exclusion file:', error.message);\n}\n\n// Return the selected URL along with metadata\nreturn [{ json: { \n  selectedPostURL: selectedPostURL,\n  randomIndex: randomIndex,\n  totalPosts: totalPosts,\n  availablePosts: availableUrls.length,\n  excludedCount: excludedUrls.length,\n  mainBlogURL: mainBlogURL\n}}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1260, -200], "id": "63f9d0e1-2669-4d1a-aa66-918704eb45b3", "name": "Random Selection"}, {"parameters": {"jsCode": "// Get RSS XML from Fetch RSS Feed\nconst rssXml = $node['Fetch RSS Feed'].json.data;\nconst mainBlogURL = $input.first().json.mainBlogURL;\n\nconsole.log('Extracting URLs from RSS XML...');\nconsole.log('RSS XML length:', rssXml.length);\n\n// Parse RSS XML to extract blog post URLs\n// Look for <link> tags that contain blog post URLs\nconst linkMatches = rssXml.match(/<link[^>]*>([^<]+)<\\/link>/g) || [];\nconsole.log('Found', linkMatches.length, 'link tags');\n\n// Also look for alternate link format in entries\nconst entryMatches = rssXml.match(/<entry[\\s\\S]*?<\\/entry>/g) || [];\nconsole.log('Found', entryMatches.length, 'entry blocks');\n\nlet postUrls = [];\n\n// Extract URLs from link tags\nlinkMatches.forEach(match => {\n  const url = match.replace(/<link[^>]*>/, '').replace(/<\\/link>/, '').trim();\n  if (url.includes('elthosrpg.blogspot.com') && url.includes('/20') && url.endsWith('.html') && !postUrls.includes(url)) {\n    postUrls.push(url);\n    console.log('Added URL from link:', url);\n  }\n});\n\n// Extract URLs from entry blocks (alternative method)\nentryMatches.forEach(entry => {\n  // Look for alternate link with rel=\"alternate\"\n  const altLinkMatch = entry.match(/<link[^>]+rel=[\"']alternate[\"'][^>]+href=[\"']([^\"']+)[\"']/i);\n  if (altLinkMatch) {\n    const url = altLinkMatch[1];\n    if (url.includes('elthosrpg.blogspot.com') && url.includes('/20') && url.endsWith('.html') && !postUrls.includes(url)) {\n      postUrls.push(url);\n      console.log('Added URL from entry:', url);\n    }\n  }\n});\n\nconsole.log('Total unique URLs extracted:', postUrls.length);\n\n// If no URLs found, show debug info\nif (postUrls.length === 0) {\n  console.log('No URLs found! RSS XML preview:', rssXml.substring(0, 1000));\n  return [{ \n    postUrls: [],\n    totalPosts: 0,\n    mainBlogURL: mainBlogURL,\n    error: 'No URLs found in RSS feed',\n    rssPreview: rssXml.substring(0, 1000)\n  }];\n}\n\nreturn [{ \n  postUrls: postUrls,\n  totalPosts: postUrls.length,\n  mainBlogURL: mainBlogURL,\n  extractionMethod: 'rss_feed'\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1060, -200], "id": "b42d8fd6-5673-4fdc-8fdd-2b675b0bc3ba", "name": "Extract Post URLs"}, {"parameters": {"url": "={{ $json.rssURL }}", "responseFormat": "string", "options": {}}, "name": "Fetch RSS Feed", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [360, -200], "id": "40c12ed9-47e2-4d68-9e08-654690c06192"}, {"parameters": {"assignments": {"assignments": [{"id": "356721e2-5dce-41fa-9118-1891bca27394", "name": "mainBlogURL", "value": "https://elthosrpg.blogspot.com/", "type": "string"}, {"id": "456721e2-5dce-41fa-9118-1891bca27395", "name": "rssURL", "value": "https://elthosrpg.blogspot.com/feeds/posts/default?max-results=999", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [180, -200], "id": "8d837417-3896-4526-a97a-b0ef365678e0", "name": "Set Main Blog URL"}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-360, -200], "id": "e7a7f4bd-3ae8-49c7-aafd-b15738091113", "name": "When clicking 'Execute workflow'", "disabled": true}, {"parameters": {"url": "={{ $json.selectedPostURL }}", "responseFormat": "string", "options": {}}, "name": "Fetch Post", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [0, 60], "id": "c9fe53ca-3a67-4ee9-9db5-d776584a8dcb"}, {"parameters": {"promptType": "define", "text": "={{ $json.firstParagraph }}", "messages": {"messageValues": [{"message": "You are a twitter expert who can take the best content from the blog entry and make the perfect tweet from it that is no more than 60 characters long. Use Text format. Include hash tags #IndieRPG #Elthos #TTRPG. Be concise and engaging."}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [740, 60], "id": "41277a04-fc9f-4b6b-9ff5-6429a15a5a2b", "name": "Basic LLM Chain", "alwaysOutputData": false}, {"parameters": {"model": {"__rl": true, "value": "deepseek-r1-distill-llama-8b", "mode": "list", "cachedResultName": "deepseek-r1-distill-llama-8b"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [740, 240], "id": "9be2e356-34b3-4cb3-a67d-17c7f515d063", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "cqdpm9ID0q2zjSkV", "name": "LM Studio"}}}, {"parameters": {"operation": "extract-content", "html": "={{ $('Fetch Post').item.json.data }}", "outputMarkdown": true}, "type": "n8n-nodes-scrapeninja.scrapeNinja", "typeVersion": 1, "position": [360, 60], "id": "7cdda613-7380-4723-87d9-2d43e26ae3ef", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"parameters": {"text": "={{ $json.text }}", "additionalFields": {}}, "type": "n8n-nodes-base.twitter", "typeVersion": 2, "position": [1260, 60], "id": "25dc21a9-fa3a-43e2-a2a7-8546849906ca", "name": "Create Tweet", "credentials": {"twitterOAuth2Api": {"id": "QMuVcnYLarrzm0jp", "name": "X account"}}}, {"parameters": {"jsCode": "// Replace \"content\" with the actual field name from ScrapeNinja output\nconst content = $input.first().json.content;\n\n// Split into paragraphs by double line breaks\nconst paragraphs = content.split(/\\n\\s*\\n/);\n\n// Get the first paragraph\nlet firstParagraph = paragraphs[2] || \"\";\n\n// Limit to 3000 words\nconst words = firstParagraph.split(/\\s+/).slice(0, 3000);\nconst limitedParagraph = words.join(\" \");\n\n// PRESERVE the blogURL field from input\nconst blogURL =   $('Random Selection').first().json.selectedPostURL \n\nconsole.log('blogURL: ' + blogURL);\n\nreturn [{ json: { \n    firstParagraph: limitedParagraph,\n    blogURL: blogURL \n}}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [540, 60], "id": "d358a0ff-c247-498a-bd7e-d715061484a2", "name": "Code - Get Paragraph 1"}, {"parameters": {"jsCode": "// Get the text from the LLM\nvar text = $input.first().json.text;\n\n// Try different ways to get the blog URL\nlet blogUrl = null;\n\n// Method 1: Try to access the Edit Field node directly\ntry {\n  const blogUrlNode = $('Edit Field - blogURL');\n  if (blogUrlNode && blogUrlNode.first()) {\n    blogUrl = blogUrlNode.first().json.blogURL;\n    console.log('Got blog URL from direct access:', blogUrl);\n  }\n} catch (error) {\n  console.log('Direct access failed');\n}\n\n// Method 2: If that doesn't work, check if it's in the input data\nif (!blogUrl && $input.first().json.blogURL) {\n  blogUrl = $input.first().json.blogURL;\n  console.log('Got blog URL from input:', blogUrl);\n}\n\n// Method 3: Hardcode as fallback (replace with your actual URL)\nif (!blogUrl) {\n  blogUrl = 'https://ElthosRPG.Blogspot.com'; // Replace with your actual blog URL\n  console.log('Using hardcoded blog URL:', blogUrl);\n}\n\nconsole.log('LLM text:', text);\nconsole.log('Final blog URL:', blogUrl);\n\n// Remove <think>...</think> and everything in between\nconst cleaned = text.replace(/<think>[\\s\\S]*?<\\/think>/, '').trim();\n\n// Clean whitespace\ntext = cleaned.replace(/^\\s+|\\s+$/g, '');\n\nif (text.length >= 2 && text.charAt(0) === '\"' && text.charAt(text.length - 1) === '\"') {\n    text = text.substring(1, text.length - 1);\n}\n\n// Twitter character limit\nconst TARGET_LIMIT = 280;\n\n// Try the full text + URL first\nconst testTweet = text + ' ' + blogUrl;\nconsole.log('Test tweet length:', testTweet.length);\n\n// If it fits within limit, use it as-is\nif (testTweet.length <= TARGET_LIMIT) {\n    console.log('Tweet fits within limit, using full text');\n    return [{ json: { text: testTweet } }];\n}\n\n// If too long, try to preserve hashtags by truncating main content\nconst hashtagRegex = /#\\w+/g;\nconst hashtags = text.match(hashtagRegex) || [];\nconst hashtagsText = hashtags.join(' ');\n\n// Remove hashtags and em-dashes from main text to see content length\nconst contentWithoutHashtags = text.replace(hashtagRegex, '').replace(/—/g, '').replace(/\\s+/g, ' ').trim();\n\n// More precise calculation: content + \" \" + hashtags + \" \" + URL = TARGET_LIMIT\n// So: content = TARGET_LIMIT - hashtags.length - URL.length - 2 spaces - 3 for \"...\"\nconst spacesNeeded = 2; // one before hashtags, one before URL\nconst ellipsisLength = 3;\nconst availableForContent = TARGET_LIMIT - hashtagsText.length - blogUrl.length - spacesNeeded - ellipsisLength;\n\nconsole.log('Available space for content:', availableForContent);\nconsole.log('Content without hashtags:', contentWithoutHashtags);\nconsole.log('Hashtags found:', hashtags);\nconsole.log('Hashtags text length:', hashtagsText.length);\n\n// If we have enough space, use truncated content + hashtags\nif (availableForContent > 15 && hashtags.length > 0) {\n    const truncatedContent = contentWithoutHashtags.substring(0, availableForContent) + '...';\n    const finalTweet = truncatedContent + ' ' + hashtagsText + ' ' + blogUrl;\n    console.log('Using truncated content with hashtags');\n    console.log('Final tweet length:', finalTweet.length);\n    return [{ json: { text: finalTweet } }];\n}\n\n// Fallback: just truncate everything\nconst maxTextLength = TARGET_LIMIT - blogUrl.length - 1 - 3;\nconst truncatedText = text.substring(0, maxTextLength) + '...';\nconst fallbackTweet = truncatedText + ' ' + blogUrl;\n\nreturn [{ json: { text: fallbackTweet } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1060, 60], "id": "6603c565-bb1c-428b-8dd2-c916d2665260", "name": "Code - Clean for Tweet"}, {"parameters": {"assignments": {"assignments": [{"id": "356721e2-5dce-41fa-9118-1891bca27394", "name": "blogURL", "value": "={{ $('Random Selection').item.json.selectedPostURL }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [180, 60], "id": "be724dfc-4c2c-40a5-aaef-86ed53e7b11b", "name": "Edit Field - blogURL"}, {"parameters": {"rule": {"interval": [{"triggerAtHour": 19}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, -200], "id": "5e993044-5332-489d-968f-89bc387cb31d", "name": "Schedule Trigger"}], "pinData": {}, "connections": {"Schedule Trigger": {"main": [[{"node": "Set Main Blog URL", "type": "main", "index": 0}]]}, "Edit Field - blogURL": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Code - Get Paragraph 1": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Code - Clean for Tweet": {"main": [[{"node": "Create Tweet", "type": "main", "index": 0}]]}, "ScrapeNinja": {"main": [[{"node": "Code - Get Paragraph 1", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}, "Basic LLM Chain": {"main": [[{"node": "Code - Clean for Tweet", "type": "main", "index": 0}]]}, "Random Selection": {"main": [[{"node": "Fetch Post", "type": "main", "index": 0}]]}, "Extract Post URLs": {"main": [[{"node": "Random Selection", "type": "main", "index": 0}]]}, "Scrape Blog Page": {"main": [[{"node": "Extract Post URLs", "type": "main", "index": 0}]]}, "Fetch RSS Feed": {"main": [[{"node": "Extract Post URLs", "type": "main", "index": 0}]]}, "Set Main Blog URL": {"main": [[{"node": "Fetch RSS Feed", "type": "main", "index": 0}]]}, "Fetch Post": {"main": [[{"node": "Edit Field - blogURL", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "6a33fe4e-cfec-4bda-ba65-a7d8f3818546", "meta": {"templateCredsSetupCompleted": true, "instanceId": "a9e00de748ec35ee88db078f832d6e48181d32e4fa741d36554310dd025f8599"}, "id": "M4PfByDgjUvSFPqZ", "tags": []}